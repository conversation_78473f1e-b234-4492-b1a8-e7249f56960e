import logger from "@/utils/logger";
import { useState, useEffect } from "react";
import { useGetTemplateByIdQuery } from "@/api/templatesApiSlice";
import type { Template } from "@/types/templates";

export interface CreateTemplateFormData {
  name: string;
  description: string;
  isActive: boolean;
}

export interface CreateTemplateValidation {
  nameError: string;
}

export interface SecondaryFolder {
  id: string;
  name: string;
  isEditing?: boolean;
}

export interface PrimaryFolder {
  id: string;
  name: string;
  isEditing?: boolean;
  expanded?: boolean;
  secondaryFolders: SecondaryFolder[];
}

export interface TreeValidation {
  duplicateNameError: string;
  deleteLastSecondaryError: string;
}

export function useManageTemplatePopup() {
  const [isOpen, setIsOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(null);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [formData, setFormData] = useState<CreateTemplateFormData>({
    name: "",
    description: "",
    isActive: true,
  });
  const [validation, setValidation] = useState<CreateTemplateValidation>({
    nameError: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch template data when editing
  const { data: templateData, isLoading: isTemplateLoading } = useGetTemplateByIdQuery(
    editingTemplateId!,
    {
      skip: !isEditMode || !editingTemplateId,
    }
  );

  // Update form data when template data is loaded
  useEffect(() => {
    if (templateData && isEditMode) {
      setFormData({
        name: templateData.name,
        description: templateData.description,
        isActive: templateData.portalBinderStatus === 1, // 1 = ACTIVE, 2 = INACTIVE
      });
    }
  }, [templateData, isEditMode]);

  const openPopup = () => {
    setIsOpen(true);
    setIsEditMode(false);
    setEditingTemplate(null);
    setFormData({
      name: "",
      description: "",
      isActive: true,
    });
    setValidation({
      nameError: "",
    });
  };

  const [popupKey, setPopupKey] = useState(0);

  const handleOpenPopup = () => {
    setPopupKey((prev) => prev + 1);
    openPopup();
  };

  const openEditPopup = (templateId: string) => {
    setIsOpen(true);
    setIsEditMode(true);
    setEditingTemplateId(templateId);
    setEditingTemplate(null); // Will be set when template data loads
    setFormData({
      name: "",
      description: "",
      isActive: true,
    });
    setValidation({
      nameError: "",
    });
  };

  const closePopup = () => {
    setIsOpen(false);
    setIsEditMode(false);
    setEditingTemplateId(null);
    setEditingTemplate(null);
    setIsSubmitting(false);
  };

  const updateField = (
    field: keyof CreateTemplateFormData,
    value: string | boolean,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (field === "name" && validation.nameError) {
      setValidation((prev) => ({
        ...prev,
        nameError: "",
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: CreateTemplateValidation = {
      nameError: "",
    };

    if (!formData.name.trim()) {
      errors.nameError = "Template Name is mandatory";
    }

    setValidation(errors);

    return !errors.nameError;
  };

  const handleCreate = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      logger.info("Creating template:", formData);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      closePopup();
    } catch (error) {
      logger.error("Failed to create template:", error as Record<string, any>);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdate = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      logger.info("Updating template:", { id: editingTemplate?.id, formData });
      await new Promise((resolve) => setTimeout(resolve, 1000));
      closePopup();
    } catch (error) {
      logger.error("Failed to update template:", error as Record<string, any>);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    closePopup();
  };

  return {
    isOpen,
    isEditMode,
    editingTemplate,
    editingTemplateId,
    formData,
    validation,
    isSubmitting,
    isTemplateLoading,
    openPopup,
    openEditPopup,
    closePopup,
    updateField,
    handleCreate,
    handleUpdate,
    handleCancel,
    handleOpenPopup,
    popupKey,
  };
}
