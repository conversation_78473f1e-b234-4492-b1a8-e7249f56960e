export interface Template {
  id: string;
  templateName: string;
  createdBy: string;
  createdOn: string;
  description: string;
  status: string;
  assignedClients: string[];
}

export interface TemplateListResponse {
  records: Template[];
  pageCount: number;
  pageNumber: number;
  pageSize: number;
  totalRecordCount: number;
}

export interface TemplateNode {
  id: string;
  name: string;
  totalChildNodes: number;
  childNodes: TemplateChildNode[];
}

export interface TemplateChildNode {
  id: string;
  name: string;
}