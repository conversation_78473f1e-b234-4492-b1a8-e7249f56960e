export interface Template {
  id: string;
  templateName: string;
  createdBy: string;
  createdOn: string;
  description: string;
  status: string;
  assignedClients: string[];
}

export interface TemplateListResponse {
  records: Template[];
  pageCount: number;
  pageNumber: number;
  pageSize: number;
  totalRecordCount: number;
}

export interface TemplateNode {
  id: string;
  name: string;
  totalChildNodes: number;
  childNodes: TemplateChildNode[];
}

export interface TemplateChildNode {
  id: string;
  name: string;
}

export interface TemplateDetailResponse {
  name: string;
  description: string;
  portalBinderStatus: number; // enum ACTIVE(1), INACTIVE(2)
  nodes: TemplateNode[];
}