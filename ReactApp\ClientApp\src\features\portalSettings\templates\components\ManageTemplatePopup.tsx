import { Dialog, DialogActionsBar } from "@progress/kendo-react-dialogs";
import { Button } from "@progress/kendo-react-buttons";
import CreateTemplateForm from "./CreateTemplateForm";
import CreateTemplateTreeView from "./CreateTemplateTreeView";
import { useTemplateTreeView } from "../hooks/useTemplateTreeView";
import { Loader } from "@progress/kendo-react-indicators";
import type {
  CreateTemplateFormData,
  CreateTemplateValidation,
  SecondaryFolder,
} from "../hooks/useManageTemplatePopup";
import type { Template } from "@/types/templates";
import logger from "@/utils/logger";

interface ManageTemplatePopupProps {
  isOpen: boolean;
  isEditMode?: boolean;
  editingTemplate?: Template | null;
  editingTemplateId?: string | null;
  formData: CreateTemplateFormData;
  validation: CreateTemplateValidation;
  isSubmitting: boolean;
  isTemplateLoading?: boolean;
  onFieldChange: (
    _field: keyof CreateTemplateFormData,
    _value: string | boolean,
  ) => void;
  onCreate: () => void;
  onUpdate?: () => void;
  onCancel: () => void;
}

export default function ManageTemplatePopup({
  isOpen,
  isEditMode = false,
  editingTemplate,
  editingTemplateId,
  formData,
  validation,
  isSubmitting,
  isTemplateLoading = false,
  onFieldChange,
  onCreate,
  onUpdate,
  onCancel,
}: ManageTemplatePopupProps) {
  const {
    primaryFolders,
    validation: treeValidation,
    isLoading: isTreeLoading,
    canDeleteFolder,
    addPrimaryFolder,
    addSecondaryFolder,
    editFolder,
    saveFolder,
    cancelEdit,
    deleteFolder,
    toggleExpand,
  } = useTemplateTreeView({
    templateId: editingTemplateId || undefined,
    isEditMode,
  });

  const isAnyFolderEditing = primaryFolders.some(
    (pf) => pf.isEditing || pf.secondaryFolders.some((sf: SecondaryFolder) => sf.isEditing),
  );

  const isCreateDisabled =
    isSubmitting ||
    !formData.name.trim() ||
    !!validation.nameError ||
    !!treeValidation.duplicateNameError ||
    !!treeValidation.deleteLastSecondaryError ||
    isAnyFolderEditing;

  const handleSubmitClick = () => {
    logger.info("Form Data:", formData);
    logger.info("Folder Structure:", primaryFolders);
    if (isEditMode) {
      logger.info("Editing template:", editingTemplate as Record<string, any>);
      onUpdate?.();
    } else {
      onCreate();
    }
  };

  if (!isOpen) return null;

  // Show loading indicator while template or tree data is being loaded in edit mode
  if (isEditMode && (isTemplateLoading || isTreeLoading)) {
    return (
      <Dialog
        onClose={onCancel}
        title="Edit Template"
        className="create-template-popup"
        width={950}
        height={450}
      >
        <div className="popup-content" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
          <Loader size="large" />
        </div>
      </Dialog>
    );
  }

  return (
    <Dialog
      onClose={onCancel}
      title={isEditMode ? "Edit Template" : "Create Template"}
      className="create-template-popup"
      width={950}
      height={450}
    >
      <div className="popup-content">
        <div className="popup-left">
          <CreateTemplateForm
            formData={formData}
            validation={validation}
            onFieldChange={onFieldChange}
          />
        </div>

        <div className="popup-right">
          <CreateTemplateTreeView
            primaryFolders={primaryFolders}
            validation={treeValidation}
            canDeleteFolder={canDeleteFolder}
            onAddPrimaryFolder={addPrimaryFolder}
            onAddSecondaryFolder={addSecondaryFolder}
            onEditFolder={editFolder}
            onSaveFolder={saveFolder}
            onCancelEdit={cancelEdit}
            onDeleteFolder={deleteFolder}
            onToggleExpand={toggleExpand}
          />
        </div>
      </div>

      <DialogActionsBar layout="end">
        <Button onClick={onCancel} disabled={isSubmitting} fillMode="flat">
          Cancel
        </Button>
        <Button
          themeColor="primary"
          onClick={handleSubmitClick}
          disabled={isCreateDisabled}
        >
          {isSubmitting ? (
            <Loader size="small" type="infinite-spinner" themeColor="primary" />
          ) : (
            isEditMode ? "Update" : "Create"
          )}
        </Button>
      </DialogActionsBar>
    </Dialog>
  );
}
